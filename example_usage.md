# Пример использования API массового пополнения баланса водителей

## Описание задачи

Необходимо вручную изменить баланс для списка водителей:
- Увеличить баланс на +100 сум
- Аннулировать долги (если баланс отрицательный)

## Решение

Созданы два API endpoint:

### 1. JSON API (`/v1/drivers/bulk-refill-balance`)
Принимает список ID водителей в JSON формате.

### 2. Excel Upload API (`/v1/drivers/bulk-refill-balance/excel`)
Принимает Excel файл с ID водителей в первой колонке.

Оба endpoint:
1. Получают список ID водителей (из JSON или Excel файла)
2. Для каждого водителя получают текущий баланс от биллинг-клик
3. Рассчитывают сумму пополнения (100 сум + долг, если есть)
4. Выполняют пополнение через биллинг-клик
5. Возвращают результат с успешными и неудачными операциями

## Примеры запросов

### 1. JSON API
```bash
curl -X POST "http://localhost:8080/v1/drivers/bulk-refill-balance" \
  -H "Content-Type: application/json" \
  -d '{
    "driver_ids": [1001, 1002, 1003, 1004, 1005]
  }'
```

### 2. Excel Upload API
```bash
curl -X POST "http://localhost:8080/v1/drivers/bulk-refill-balance/excel" \
  -F "file=@drivers.xlsx"
```

#### Структура Excel файла:
```
| A        |
|----------|
| driver_id|  <- заголовок (опционально)
| 1001     |
| 1002     |
| 1003     |
| 1004     |
| 1005     |
```

### Пример ответа
```json
{
  "success": true,
  "processed_ids": [1001, 1002, 1003],
  "failed_ids": [1004, 1005],
  "message": "Частично выполнено: 3 успешно, 2 неудачно"
}
```

## Логика работы

### Для водителя с положительным балансом
- Текущий баланс: +5000 тийинов (50 сум)
- Пополнение: +10000 тийинов (100 сум)
- Итоговый баланс: +15000 тийинов (150 сум)

### Для водителя с отрицательным балансом (долг)
- Текущий баланс: -3000 тийинов (-30 сум)
- Пополнение: +13000 тийинов (100 сум + 30 сум долга)
- Итоговый баланс: +10000 тийинов (100 сум)

### Для водителя с нулевым балансом
- Текущий баланс: 0 тийинов
- Пополнение: +10000 тийинов (100 сум)
- Итоговый баланс: +10000 тийинов (100 сум)

## Технические детали

- **Валюта**: Все суммы в тийинах (1 сум = 100 тийинов)
- **Счет**: Используется дефолтный счет из конфигурации
- **Тип операции**: Используется дефолтный тип из конфигурации
- **Дедупликация**: Каждая операция имеет уникальный reqId
- **Лимиты**: Максимум 1000 водителей за один запрос

## Обработка ошибок

API возвращает HTTP 200 даже при частичных неудачах. Проверяйте поля `success`, `processed_ids` и `failed_ids` в ответе.

### Возможные причины неудач:
- Водитель не найден в биллинг-клик
- Ошибка соединения с биллинг-клик
- Недостаточно средств на счете для пополнения
- Технические ошибки биллинг-клик

## Мониторинг

Все операции логируются:
- Успешные операции: INFO уровень
- Ошибки: ERROR уровень с подробным описанием

Проверяйте логи приложения для диагностики проблем с неудачными операциями.

## Безопасность

- API не требует аутентификации (внутренний сервис)
- Ограничение на количество водителей (максимум 1000)
- Защита от дублирования операций через Redis

## Рекомендации по использованию

1. **Тестирование**: Сначала протестируйте на небольшом количестве водителей
2. **Мониторинг**: Следите за логами во время выполнения операций
3. **Повторные попытки**: Для неудачных операций можно повторить запрос с failed_ids
4. **Размер батча**: Для больших списков разбивайте на батчи по 100-500 водителей
