#!/bin/bash

# Тестовый скрипт для проверки API массового пополнения баланса водителей

# Конфигурация
API_URL="http://localhost:8080"  # Замените на актуальный URL вашего сервиса
ENDPOINT="/v1/drivers/bulk-refill-balance"

echo "=== Тестирование API массового пополнения баланса водителей (Excel) ==="
echo "URL: ${API_URL}${ENDPOINT}"
echo ""

# Тест 1: Загрузка Excel файла (валидный)
echo "Тест 1: Загрузка Excel файла (валидный)"
if [ -f "test_drivers.xlsx" ]; then
    curl -X POST "${API_URL}${ENDPOINT}" \
      -F "file=@test_drivers.xlsx" \
      -w "\nHTTP Status: %{http_code}\n" \
      -s
else
    echo "Файл test_drivers.xlsx не найден. Создайте его с помощью: go run create_test_excel.go"
fi
echo ""
echo "---"

# Тест 2: Отсутствие файла (должен вернуть ошибку)
echo "Тест 2: Отсутствие файла (должен вернуть ошибку)"
curl -X POST "${API_URL}${ENDPOINT}" \
  -w "\nHTTP Status: %{http_code}\n" \
  -s
echo ""
echo "---"

# Тест 3: Неправильный тип файла (должен вернуть ошибку)
echo "Тест 3: Неправильный тип файла (должен вернуть ошибку)"
echo "test content" > test.txt
curl -X POST "${API_URL}${ENDPOINT}" \
  -F "file=@test.txt" \
  -w "\nHTTP Status: %{http_code}\n" \
  -s
rm -f test.txt
echo ""
echo "---"

echo "=== Тестирование завершено ==="
