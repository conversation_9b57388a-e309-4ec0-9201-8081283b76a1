#!/bin/bash

# Тестовый скрипт для проверки API массового пополнения баланса водителей

# Конфигурация
API_URL="http://localhost:8080"  # Замените на актуальный URL вашего сервиса
ENDPOINT="/v1/drivers/bulk-refill-balance"

echo "=== Тестирование API массового пополнения баланса водителей ==="
echo "URL: ${API_URL}${ENDPOINT}"
echo ""

# Тест 1: Валидный запрос с несколькими водителями
echo "Тест 1: Валидный запрос с несколькими водителями"
curl -X POST "${API_URL}${ENDPOINT}" \
  -H "Content-Type: application/json" \
  -d '{
    "driver_ids": [1, 2, 3, 4, 5]
  }' \
  -w "\nHTTP Status: %{http_code}\n" \
  -s
echo ""
echo "---"

# Тест 2: Пустой массив водителей (должен вернуть ошибку)
echo "Тест 2: Пустой массив водителей (должен вернуть ошибку)"
curl -X POST "${API_URL}${ENDPOINT}" \
  -H "Content-Type: application/json" \
  -d '{
    "driver_ids": []
  }' \
  -w "\nHTTP Status: %{http_code}\n" \
  -s
echo ""
echo "---"

# Тест 3: Отсутствие поля driver_ids (должен вернуть ошибку)
echo "Тест 3: Отсутствие поля driver_ids (должен вернуть ошибку)"
curl -X POST "${API_URL}${ENDPOINT}" \
  -H "Content-Type: application/json" \
  -d '{}' \
  -w "\nHTTP Status: %{http_code}\n" \
  -s
echo ""
echo "---"

# Тест 4: Невалидный JSON (должен вернуть ошибку)
echo "Тест 4: Невалидный JSON (должен вернуть ошибку)"
curl -X POST "${API_URL}${ENDPOINT}" \
  -H "Content-Type: application/json" \
  -d '{invalid json}' \
  -w "\nHTTP Status: %{http_code}\n" \
  -s
echo ""
echo "---"

# Тест 5: Слишком много водителей (должен вернуть ошибку)
echo "Тест 5: Слишком много водителей (должен вернуть ошибку)"
# Создаем массив из 1001 элемента
LARGE_ARRAY=$(seq -s, 1 1001)
curl -X POST "${API_URL}${ENDPOINT}" \
  -H "Content-Type: application/json" \
  -d "{\"driver_ids\": [${LARGE_ARRAY}]}" \
  -w "\nHTTP Status: %{http_code}\n" \
  -s
echo ""
echo "---"

echo "=== Тестирование завершено ==="
