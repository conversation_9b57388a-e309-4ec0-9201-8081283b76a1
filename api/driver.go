package api

import (
	"context"
	"fmt"

	"billing_service/util"

	fiber "github.com/gofiber/fiber/v3"
	null "github.com/guregu/null/v6"
)

func (h *handler) CreateDriver(c fiber.Ctx) error {
	driverId := util.ParseInt(c.<PERSON>("driver_id"))
	if driverId == 0 {
		return h.badRequestResponse(c, "incorrect driver_id")
	}

	parkId := util.ParseInt(c.<PERSON>("park_id"))
	if driverId == 0 {
		return h.badRequestResponse(c, "incorrect park_id")
	}

	rate := util.ParseInt(c.Params("rate"))

	resp, err := h.repo.CreateDriver(c, driverId, parkId, rate, c.Params("phone"), c.<PERSON>("first_name"), c.<PERSON>("last_name"), c.<PERSON><PERSON>("start_date"))
	if err != nil {
		return h.serviceErrorResponse(c, err.<PERSON>rror())
	}

	return c.<PERSON>(resp)
}

func (h *handler) UpdateDriver(c fiber.Ctx) error {
	driverId := util.ParseInt(c.Params("driver_id"))
	if driverId == 0 {
		return h.badRequestResponse(c, "incorrect driver_id")
	}

	parkId := util.ParseInt(c.Params("park_id"))
	if driverId == 0 {
		return h.badRequestResponse(c, "incorrect park_id")
	}

	rate := util.ParseInt(c.Params("rate"))

	resp, err := h.repo.UpdateDriver(c, driverId, parkId, rate, c.Params("phone"), c.Params("first_name"), c.Params("last_name"), c.Params("start_date"))
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(resp)
}

func (h *handler) ActivateDriver(c fiber.Ctx) error {
	driverId := util.ParseInt(c.Params("driver_id"))
	if driverId == 0 {
		return h.badRequestResponse(c, "incorrect driver_id")
	}

	resp, err := h.repo.ActivateDriver(c, driverId)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(resp)
}

func (h *handler) DeactivateDriver(c fiber.Ctx) error {
	driverId := util.ParseInt(c.Params("driver_id"))
	if driverId == 0 {
		return h.badRequestResponse(c, "incorrect driver_id")
	}

	resp, err := h.repo.DeactivateDriver(c, driverId)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(resp)
}

func (h *handler) GetDriverCard(c fiber.Ctx) error {
	driverId := util.ParseInt(c.Params("driver_id"))
	if driverId == 0 {
		return h.badRequestResponse(c, "incorrect driver_id")
	}

	resp, err := h.app.GetDriverCard(c, driverId)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(map[string]any{"status": "success", "card": resp})
}

type AddDriverPaymeCardRequest struct {
	Number string `json:"card_number"`
	Token  string `json:"token"`
}

func (h *handler) AddDriverCard(c fiber.Ctx) error {
	driverId := util.ParseInt(c.Params("driver_id"))
	if driverId == 0 {
		return h.badRequestResponse(c, "incorrect driver_id")
	}

	var req AddDriverPaymeCardRequest

	err := c.Bind().JSON(&req)
	if err != nil {
		return h.badRequestResponse(c, "incorrect request body: "+err.Error())
	}

	errType, err := h.app.AddDriverCard(c, driverId, req.Token, req.Number)
	if err != nil {
		if errType != "" {
			return h.errorResponse(c, errType, err.Error())
		} else {
			return h.serviceErrorResponse(c, err.Error())
		}
	}

	return c.JSON(map[string]string{"status": "success"})
}

func (h *handler) DeleteDriverCard(c fiber.Ctx) error {
	driverId := util.ParseInt(c.Params("driver_id"))
	if driverId == 0 {
		return h.badRequestResponse(c, "incorrect driver_id")
	}

	err := h.app.DeleteDriverCard(c, driverId)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(map[string]string{"status": "success"})
}

func (h *handler) GetDriverBalance(c fiber.Ctx) error {
	driverId := util.ParseInt(c.Params("driver_id"))
	if driverId == 0 {
		return h.badRequestResponse(c, "incorrect driver_id")
	}

	resp, err := h.repo.GetDriverBalance(c, driverId)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(resp)
}

func (h *handler) GetDriverBalanceHistory(c fiber.Ctx) error {
	driverId := util.ParseInt(c.Params("driver_id"))

	if driverId <= 0 {
		return h.badRequestResponse(c, "Invalid driver_id")
	}

	page := util.ParseInt(c.Query("page"))
	if page <= 0 {
		page = 1
	}

	limit := util.ParseInt(c.Query("limit"))
	if limit <= 0 {
		limit = 20
	}

	resp, err := h.repo.GetDriverBalanceHistory(c, driverId, limit, (page-1)*limit)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(resp)
}

func (h *handler) GetDriverBalanceHistoryV2(c fiber.Ctx) error {
	driverId := util.ParseInt(c.Params("driver_id"))
	if driverId == 0 {
		return h.badRequestResponse(c, "incorrect driver_id")
	}

	limit := util.ParseInt(c.Params("limit"))
	if limit == 0 {
		limit = 20
	}

	skip := util.ParseInt(c.Params("skip"))

	resp, err := h.repo.GetDriverBalanceHistoryV2(c, driverId, limit, skip)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(resp)
}

type DriverMoneyTransferRequest struct {
	ReceiverId int `json:"receiver_id"`
	Amount     int `json:"amount"`
}

func (h *handler) DriverMoneyTransfer(c fiber.Ctx) error {
	driverId := util.ParseInt(c.Params("driver_id"))
	if driverId == 0 {
		return h.badRequestResponse(c, "incorrect driver_id")
	}

	var req DriverMoneyTransferRequest

	err := c.Bind().JSON(&req)
	if err != nil {
		return h.badRequestResponse(c, "incorrect request body: "+err.Error())
	}

	if req.Amount <= 0 {
		return h.badRequestResponse(c, "incorrect amount")
	}

	result, errType, err := h.app.DriverMoneyTransferCreate(c, driverId, req.ReceiverId, req.Amount, util.GetAppLanguage(c))
	if err != nil {
		if errType != "" {
			return h.errorResponse(c, errType, err.Error())
		} else {
			return h.serviceErrorResponse(c, err.Error())
		}
	}

	return c.JSON(result)
}

type DriverMoneyTransferConfirmRequest struct {
	Code       int `json:"code"`
	TransferId int `json:"transfer_id"`
}

func (h *handler) DriverMoneyTransferConfirm(c fiber.Ctx) error {
	driverId := util.ParseInt(c.Params("driver_id"))
	if driverId == 0 {
		return h.badRequestResponse(c, "incorrect driver_id")
	}

	var req DriverMoneyTransferConfirmRequest

	err := c.Bind().JSON(&req)
	if err != nil {
		return h.badRequestResponse(c, "incorrect request body: "+err.Error())
	}

	if req.TransferId <= 0 {
		return h.badRequestResponse(c, "incorrect transfer_id")
	}

	errType, err := h.app.DriverMoneyTransferConfirm(c, driverId, req.TransferId, req.Code)
	if err != nil {
		if errType != "" {
			return h.errorResponse(c, errType, err.Error())
		} else {
			return h.serviceErrorResponse(c, err.Error())
		}
	}

	return c.JSON(fiber.StatusOK)
}

type DriverRefineRequest struct {
	Comment null.String `json:"description"`
}

func (h *handler) DriverRefineBalance(c fiber.Ctx) error {
	driverId := util.ParseInt(c.Params("driver_id"))
	if driverId == 0 {
		return h.badRequestResponse(c, "incorrect driver_id")
	}

	amount := util.ParseInt(c.Params("amount"))
	if amount == 0 {
		return h.badRequestResponse(c, "incorrect amount")
	}

	account := util.ParseInt(c.Params("account"))

	var req DriverRefineRequest

	_ = c.Bind().JSON(&req)

	if req.Comment.String == "" {
		req.Comment.String = "Списание средств из баланса"
	}

	reqId := fmt.Sprintf("driver:%d:refine:%d:to_account:%d", driverId, amount, account)
	err := h.app.RefineDriverBalance(context.Background(), driverId, amount, account, req.Comment.String, reqId)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(fiber.StatusOK)
}

func (h *handler) BlockDriverForLowBalance(c fiber.Ctx) error {
	driverId := util.ParseInt(c.Params("driver_id"))
	if driverId == 0 {
		return h.badRequestResponse(c, "incorrect driver_id")
	}

	err := h.app.BlockDriverForLowBalance(c, driverId)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(fiber.StatusOK)
}

func (h *handler) GetDriverFallbackLinks(c fiber.Ctx) error {
	driverId := util.ParseInt(c.Params("driver_id"))
	if driverId == 0 {
		return h.badRequestResponse(c, "incorrect driver_id")
	}

	result, err := h.app.GetDriverFallbackLinks(c, driverId)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(result)
}

func (h *handler) RefillDriverBalance(c fiber.Ctx) error {
	driverId := util.ParseInt(c.Params("driver_id"))
	amount := util.ParseInt(c.Params("amount"))
	amountType := util.ParseInt(c.Params("amount_type"))
	accountId := util.ParseInt(c.Params("account_number"))
	comment := c.Params("description")

	if driverId == 0 {
		return h.badRequestResponse(c, "incorrect driver_id")
	}
	if amount <= 0 {
		return h.badRequestResponse(c, "amount must be positive")
	}
	if amountType <= 0 {
		return h.badRequestResponse(c, "amount_type must be positive")
	}
	if comment == "" {
		return h.badRequestResponse(c, "description must be provided")
	}

	reqId := fmt.Sprintf("refill_driver:%d:balance:amount:%damounttype:%d:comment:%s", driverId, amount, amountType, comment)

	err := h.app.RefillDriverBalance(c, driverId, amount, amountType, accountId, comment, reqId)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(fiber.StatusOK)
}

func (h *handler) UbkPing(c fiber.Ctx) (err error) {
	err = h.app.UbkPing(c)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}
	return c.JSON(fiber.StatusOK)
}

func (h *handler) BulkRefillDriverBalanceFromExcel(c fiber.Ctx) error {
	file, err := c.FormFile("file")
	if err != nil {
		return h.badRequestResponse(c, "файл не найден в запросе")
	}

	if file.Header.Get("Content-Type") != "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" &&
		file.Header.Get("Content-Type") != "application/vnd.ms-excel" {
		return h.badRequestResponse(c, "поддерживаются только Excel файлы (.xlsx, .xls)")
	}

	// Проверяем размер файла (максимум 10MB)
	if file.Size > 10*1024*1024 {
		return h.badRequestResponse(c, "размер файла не должен превышать 10MB")
	}

	// Открываем файл
	fileReader, err := file.Open()
	if err != nil {
		return h.serviceErrorResponse(c, "не удалось открыть загруженный файл")
	}
	defer fileReader.Close()

	driverIds, err := h.app.ParseDriverIdsFromExcel(fileReader)
	if err != nil {
		return h.badRequestResponse(c, err.Error())
	}

	if len(driverIds) == 0 {
		return h.badRequestResponse(c, "в файле не найдено валидных ID водителей")
	}

	if len(driverIds) > 1000 {
		return h.badRequestResponse(c, "слишком много водителей в файле, максимум 1000 разрешено")
	}

	resp, err := h.app.BulkRefillDriverBalance(c, driverIds)
	if err != nil {
		return h.serviceErrorResponse(c, err.Error())
	}

	return c.JSON(resp)
}
