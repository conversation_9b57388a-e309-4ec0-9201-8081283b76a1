package app

import (
	"context"
	"fmt"
	"time"

	"billing_service/model"

	redis "github.com/redis/go-redis/v9"
)

func (a *App) RefillDriverBalance(ctx context.Context, driverId, amount, amountType, accountId int, comment, reqId string) (err error) {
	exists, err := a.setRequestID(ctx, reqId)
	if err != nil || exists {
		return
	}

	if amountType == 0 {
		amountType = a.cfg.Defaults.PaymePaymentTypeFromMytaxiOutsourceBilling
	}

	if accountId == 0 {
		accountId = a.cfg.Defaults.BillingPaycomAccountNum
	}

	balance, err := a.repo.RefillDriverBalance(ctx, driverId, amount, amountType, accountId, comment)
	if err != nil {
		_ = a.redis.Del(ctx, reqId)
		return
	}

	if balance >= a.repo.GetSysParam("driver_min_balance_for_blocking", "0").Int() {
		err = a.unblockDriverForRefillBalance(ctx, driverId)
		if err != nil {
			a.log.Errorf("unblock driver %d: %v", driverId, err)
			err = nil // TODO make async
		}
	}

	return
}

func (a *App) RefineDriverBalance(ctx context.Context, driverId, amount, account int, comment string, reqId string) (err error) {
	exists, err := a.setRequestID(ctx, reqId)
	if err != nil || exists {
		return
	}

	if account == 0 {
		account = a.cfg.Defaults.BillingDeductionAccountNum
	}

	balance, err := a.repo.RefineDriverBalance(ctx, driverId, amount, account, comment)
	if err != nil {
		_ = a.redis.Del(ctx, reqId)
		return err
	}

	if balance < a.repo.GetSysParam("driver_min_balance_for_blocking", "0").Int() {
		err = a.blockDriverForLowBalance(ctx, driverId)
		if err != nil {
			a.log.Errorf("block driver %d: %v", err)
			err = nil // TODO make async
		}
	}

	return
}

func (a *App) BlockDriverForLowBalance(ctx context.Context, driverId int) (err error) {
	exists, err := a.repo.GetDriverExists(ctx, driverId)
	if err != nil {
		return fmt.Errorf("get driver exists: %v", err)
	}
	if !exists {
		return fmt.Errorf("driver %d does not exist", driverId)
	}

	err = a.checkAndBlockDriverForLowBalance(ctx, driverId)
	if err != nil {
		return fmt.Errorf("check and block driver for low balance: %v", err)
	}

	return
}

func (a *App) setRequestID(ctx context.Context, reqId string) (exists bool, err error) {
	val, err := a.redis.SetArgs(ctx, reqId, 1, redis.SetArgs{Get: true, TTL: 10 * time.Minute, Mode: "NX"}).Result()
	if err != nil {
		if err == redis.Nil {
			err = nil
		} else {
			err = fmt.Errorf("redis get and set: %v", err)
		}
		return
	}

	if val != "" {
		exists = true
	}

	return
}

func (a *App) checkAndBlockDriverForLowBalance(ctx context.Context, driverId int) (err error) {
	balance, err := a.repo.GetDriverBalance(ctx, driverId)
	if balance.Balance >= a.repo.GetSysParam("driver_min_balance_for_blocking", "-10000").Int() {
		return
	}
	success, err := a.repo.BlockDriver(ctx, driverId)
	if err != nil {
		return
	}
	if success {
		err = a.nats.Publish("drivers.active_status", "", model.DriverStatus{DriverId: driverId, Status: false})
		if err != nil {
			a.log.Errorf("publish driver active status to message broker: %v", err)
			err = nil
		}
		a.repo.SendDriverNotification(driverId, 0, 0, fmt.Sprintf("driver:%d:blocked", driverId), "driver_blocked_for_low_balance")
	}
	return
}

func (a *App) blockDriverForLowBalance(ctx context.Context, driverId int) (err error) {
	success, err := a.repo.BlockDriver(ctx, driverId)
	if err != nil {
		return
	}
	if success {
		err = a.nats.Publish("drivers.active_status", "", model.DriverStatus{DriverId: driverId, Status: false})
		if err != nil {
			a.log.Errorf("publish driver active status to message broker: %v", err)
			err = nil
		}
		a.repo.SendDriverNotification(driverId, 0, 0, fmt.Sprintf("driver:%d:blocked", driverId), "driver_blocked_for_low_balance")
	}
	return
}

// BulkRefillDriverBalance пополняет баланс для списка водителей на +100 сум и аннулирует долги
func (a *App) BulkRefillDriverBalance(ctx context.Context, driverIds []int) (resp model.BulkRefillDriverBalanceResponse, err error) {
	const refillAmount = 10000 // 100 сум в тийинах (100 * 100)
	const comment = "Массовое пополнение баланса +100 сум"

	resp.ProcessedIds = make([]int, 0)
	resp.FailedIds = make([]int, 0)

	// Используем дефолтные значения из конфигурации
	amountType := a.cfg.Defaults.PaymePaymentTypeFromMytaxiOutsourceBilling
	accountId := a.cfg.Defaults.BillingPaycomAccountNum

	for _, driverId := range driverIds {
		// Получаем текущий баланс водителя
		currentBalance, err := a.repo.GetDriverBalance(ctx, driverId)
		if err != nil {
			a.log.Errorf("failed to get balance for driver %d: %v", driverId, err)
			resp.FailedIds = append(resp.FailedIds, driverId)
			continue
		}

		// Рассчитываем сумму для пополнения
		// Если баланс отрицательный (есть долг), добавляем его к сумме пополнения
		totalRefillAmount := refillAmount
		if currentBalance.Balance < 0 {
			totalRefillAmount += -currentBalance.Balance // добавляем абсолютное значение долга
		}

		// Создаем уникальный reqId для каждого водителя
		reqId := fmt.Sprintf("bulk_refill_driver:%d:amount:%d:timestamp:%d", driverId, totalRefillAmount, time.Now().UnixMilli())

		// Пополняем баланс
		err = a.RefillDriverBalance(ctx, driverId, totalRefillAmount, amountType, accountId, comment, reqId)
		if err != nil {
			a.log.Errorf("failed to refill balance for driver %d: %v", driverId, err)
			resp.FailedIds = append(resp.FailedIds, driverId)
			continue
		}

		resp.ProcessedIds = append(resp.ProcessedIds, driverId)
		a.log.Infof("successfully refilled balance for driver %d: amount=%d", driverId, totalRefillAmount)
	}

	// Определяем успешность операции
	if len(resp.ProcessedIds) > 0 {
		resp.Success = true
		if len(resp.FailedIds) > 0 {
			resp.Message = fmt.Sprintf("Частично выполнено: %d успешно, %d неудачно", len(resp.ProcessedIds), len(resp.FailedIds))
		} else {
			resp.Message = fmt.Sprintf("Все операции выполнены успешно: %d водителей", len(resp.ProcessedIds))
		}
	} else {
		resp.Success = false
		resp.Message = "Не удалось пополнить баланс ни одному водителю"
	}

	return
}

func (a *App) unblockDriverForRefillBalance(ctx context.Context, driverId int) (err error) {
	success, err := a.repo.UnblockDriver(ctx, driverId)
	if err != nil {
		return
	}
	if success {
		err = a.nats.Publish("drivers.active_status", "", model.DriverStatus{DriverId: driverId, Status: true})
		if err != nil {
			a.log.Errorf("publish driver active status to message broker: %v", err)
			err = nil
		}
		a.repo.SendDriverNotification(driverId, 0, 0, fmt.Sprintf("driver:%d:unblocked", driverId), "driver_unblocked_for_refill_balance")
	}
	return
}
